using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Graph;
using System.Net;
using System.Text.Json;
using PasswordHistoryValidator.Shared;
using PasswordHistoryValidator.Services;
using Azure.Storage.Blobs;

namespace PasswordHistoryValidator;

public class UtilityFunction : BaseFunctionService
{
    private readonly ILogger<UtilityFunction> _logger;
    private readonly BlobServiceClient _blobServiceClient;
    private readonly IPasswordHistoryService _passwordHistoryService;
    private readonly IEmailService _emailService;
    private readonly GraphServiceClient _graphServiceClient;

    public UtilityFunction(
        ILogger<UtilityFunction> logger,
        BlobServiceClient blobServiceClient,
        IPasswordHistoryService passwordHistoryService,
        IEmailService emailService,
        GraphServiceClient graphServiceClient,
        JsonSerializerOptions jsonOptions) : base(jsonOptions)
    {
        _logger = logger;
        _blobServiceClient = blobServiceClient;
        _passwordHistoryService = passwordHistoryService;
        _emailService = emailService;
        _graphServiceClient = graphServiceClient;
    }

    [Function("UtilityService")]
    public async Task<HttpResponseData> Run(
        [HttpTrigger(AuthorizationLevel.Function, "post", "options")] HttpRequestData req,
        CancellationToken cancellationToken)
    {
        var correlationId = GenerateCorrelationId();

        if (req.Method.Equals("OPTIONS", StringComparison.OrdinalIgnoreCase))
        {
            return CreateCorsResponse(req);
        }

        try
        {
            var operation = req.Query["operation"];
            if (string.IsNullOrEmpty(operation))
            {
                return await CreateErrorResponse(req, "Operation parameter required", correlationId);
            }

            return operation.ToLower() switch
            {
                "health" => await HandleHealthCheck(req, correlationId),
                "cleanup-tokens" => await HandleTokenCleanup(req, correlationId, cancellationToken),
                "stats" => await HandleSystemStats(req, correlationId, cancellationToken),
                "notify-expiring-passwords" => await HandlePasswordExpirationNotifications(req, correlationId, cancellationToken),
                _ => await CreateErrorResponse(req, $"Invalid operation: {operation}", correlationId)
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Utility service error [CorrelationId: {CorrelationId}]", correlationId);
            return await CreateErrorResponse(req, "Service error", correlationId);
        }
    }

    private async Task<HttpResponseData> HandleHealthCheck(HttpRequestData req, string correlationId)
    {
        var healthStatus = new
        {
            status = "healthy",
            timestamp = DateTime.UtcNow,
            version = "3.0.0-simplified",
            services = new
            {
                blobStorage = await CheckBlobStorageHealth(),
                configuration = "healthy"
            }
        };

        return await CreateJsonResponse(req, healthStatus, HttpStatusCode.OK, correlationId);
    }

    private async Task<HttpResponseData> HandleTokenCleanup(HttpRequestData req, string correlationId, CancellationToken cancellationToken)
    {
        var cleanupResults = await CleanupExpiredTokens(cancellationToken);

        return await CreateJsonResponse(req, new
        {
            message = "Token cleanup completed",
            tokensRemoved = cleanupResults.TokensRemoved,
            tokensProcessed = cleanupResults.TokensProcessed,
            timestamp = DateTime.UtcNow
        }, HttpStatusCode.OK, correlationId);
    }

    private async Task<HttpResponseData> HandleSystemStats(HttpRequestData req, string correlationId, CancellationToken cancellationToken)
    {
        var stats = await GetSystemStatistics(cancellationToken);
        return await CreateJsonResponse(req, stats, HttpStatusCode.OK, correlationId);
    }

    private async Task<string> CheckBlobStorageHealth()
    {
        try
        {
            var containerClient = _blobServiceClient.GetBlobContainerClient("passwordhistory");
            await containerClient.CreateIfNotExistsAsync();
            return "healthy";
        }
        catch
        {
            return "unhealthy";
        }
    }

    private async Task<CleanupResults> CleanupExpiredTokens(CancellationToken cancellationToken)
    {
        var results = new CleanupResults();
        var containerClient = _blobServiceClient.GetBlobContainerClient("resettokens");

        await foreach (var blobItem in containerClient.GetBlobsAsync(cancellationToken: cancellationToken))
        {
            results.TokensProcessed++;

            // individual token processing errors
            try
            {
                var blobClient = containerClient.GetBlobClient(blobItem.Name);
                var downloadResult = await blobClient.DownloadContentAsync(cancellationToken);
                var tokenDataJson = downloadResult.Value.Content.ToString();
                var tokenData = JsonSerializer.Deserialize<ResetTokenData>(tokenDataJson, JsonOptions);

                if (tokenData != null && (tokenData.ExpiresUtc < DateTime.UtcNow || tokenData.Used))
                {
                    await blobClient.DeleteIfExistsAsync(cancellationToken: cancellationToken);
                    results.TokensRemoved++;
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error processing token blob {BlobName} during cleanup", blobItem.Name);
            }
        }

        return results;
    }

    private async Task<HttpResponseData> HandlePasswordExpirationNotifications(HttpRequestData req, string correlationId, CancellationToken cancellationToken)
    {
        var expirationDays = int.Parse(Environment.GetEnvironmentVariable("PASSWORD_EXPIRATION_DAYS") ?? "90");
        var warningDays = int.Parse(Environment.GetEnvironmentVariable("PASSWORD_WARNING_DAYS") ?? "7");

        var results = await ProcessPasswordExpirationNotifications(expirationDays, warningDays, cancellationToken);

        return await CreateJsonResponse(req, new
        {
            message = "Password expiration notifications processed",
            usersNotified = results.UsersNotified,
            emailsSent = results.EmailsSent,
            expirationDays,
            warningDays,
            timestamp = DateTime.UtcNow
        }, HttpStatusCode.OK, correlationId);
    }

    private async Task<ExpirationNotificationResults> ProcessPasswordExpirationNotifications(int expirationDays, int warningDays, CancellationToken cancellationToken)
    {
        var results = new ExpirationNotificationResults();
        var passwordHistoryContainer = _blobServiceClient.GetBlobContainerClient("passwordhistory");

        await foreach (var blobItem in passwordHistoryContainer.GetBlobsAsync(cancellationToken: cancellationToken))
        {
            try
            {
                var blobClient = passwordHistoryContainer.GetBlobClient(blobItem.Name);
                var downloadResult = await blobClient.DownloadContentAsync(cancellationToken);
                var historyDataJson = downloadResult.Value.Content.ToString();
                var historyData = JsonSerializer.Deserialize<PasswordHistoryStorage>(historyDataJson, JsonOptions);

                if (historyData != null && !string.IsNullOrEmpty(historyData.UserId))
                {
                    var passwordAge = (DateTime.UtcNow - historyData.LastUpdatedUtc).TotalDays;
                    var daysUntilExpiration = expirationDays - (int)passwordAge;

                    // Send notification if in warning period
                    if (daysUntilExpiration <= warningDays && daysUntilExpiration > 0)
                    {
                        var emailSent = await SendExpirationNotification(historyData.UserId, daysUntilExpiration);
                        if (emailSent)
                        {
                            results.EmailsSent++;
                            results.UsersNotified++;
                        }
                    }
                    // Handle expired passwords
                    else if (daysUntilExpiration <= 0)
                    {
                        var emailSent = await SendExpiredNotification(historyData.UserId);
                        if (emailSent)
                        {
                            results.EmailsSent++;
                            results.UsersNotified++;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error processing password history blob {BlobName} during expiration check", blobItem.Name);
            }
        }

        return results;
    }

    private async Task<bool> CheckAbsenceDuringExpirationPeriod(string userId, int warningDays)
    {
        try
        {
            // Check if GraphServiceClient is available
            if (_graphServiceClient == null)
            {
                _logger.LogWarning("Cannot check user absence: Entra External ID is not configured. Assuming user is not absent for {UserId}", userId);
                return false; // Default to not absent when service is unavailable
            }

            // Extract email and application from scoped userId (applicationName/email)
            var parts = userId.Split('/');
            var email = parts.Length > 1 ? parts[1] : userId;
            var applicationName = parts.Length > 1 ? parts[0] : "Default Application";

            // Query Graph API for recent sign-ins
            var emailEsc = ODataHelpers.EscapeString(email);
            var appEsc = ODataHelpers.EscapeString(applicationName);
            var filter = $"userPrincipalName eq '{emailEsc}' and appDisplayName eq '{appEsc}' and status/errorCode eq 0";

            var signIns = await _graphServiceClient.AuditLogs.SignIns
                .GetAsync(requestConfiguration =>
                {
                    requestConfiguration.QueryParameters.Filter = filter;
                    requestConfiguration.QueryParameters.Top = 1;
                    requestConfiguration.QueryParameters.Orderby = new[] { "createdDateTime desc" };
                });

            var lastLogin = signIns?.Value?.FirstOrDefault()?.CreatedDateTime;
            if (!lastLogin.HasValue) return true; // No login found = absent

            var warningStartDate = DateTime.UtcNow.AddDays(-warningDays);
            return lastLogin.Value < warningStartDate;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error checking absence for user {UserId}", userId);
            return false; // Default to not absent on error
        }
    }

    private async Task<bool> SendExpirationNotification(string userId, int daysUntilExpiration)
    {
        try
        {
            // Extract email from userId (format: applicationName/email)
            var parts = userId.Split('/');
            var email = parts.Length > 1 ? parts[1] : userId;
            var applicationName = parts.Length > 1 ? parts[0] : "Default Application";

            // Check for absence during warning period
            var isAbsent = await CheckAbsenceDuringExpirationPeriod(userId, 15);

            return await _emailService.SendPasswordExpirationNotificationAsync(
                email, applicationName, daysUntilExpiration, Guid.NewGuid().ToString(), isAbsent);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send expiration notification for user {UserId}", userId);
            return false;
        }
    }

    private async Task<bool> SendExpiredNotification(string userId)
    {
        try
        {
            // Extract email from userId (format: applicationName/email)
            var parts = userId.Split('/');
            var email = parts.Length > 1 ? parts[1] : userId;
            var applicationName = parts.Length > 1 ? parts[0] : "Default Application";

            return await _emailService.SendPasswordExpiredNotificationAsync(
                email, applicationName, Guid.NewGuid().ToString());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send expired notification for user {UserId}", userId);
            return false;
        }
    }

    private async Task<object> GetSystemStatistics(CancellationToken cancellationToken)
    {
        var passwordHistoryContainer = _blobServiceClient.GetBlobContainerClient("passwordhistory");
        var resetTokensContainer = _blobServiceClient.GetBlobContainerClient("resettokens");

        var passwordHistoryCount = 0;
        var resetTokensCount = 0;
        var activeTokensCount = 0;

        await foreach (var blobItem in passwordHistoryContainer.GetBlobsAsync(cancellationToken: cancellationToken))
        {
            passwordHistoryCount++;
        }


        await foreach (var blobItem in resetTokensContainer.GetBlobsAsync(cancellationToken: cancellationToken))
        {
            resetTokensCount++;

            // individual token processing errors
            try
            {
                var blobClient = resetTokensContainer.GetBlobClient(blobItem.Name);
                var downloadResult = await blobClient.DownloadContentAsync(cancellationToken);
                var tokenDataJson = downloadResult.Value.Content.ToString();
                var tokenData = JsonSerializer.Deserialize<ResetTokenData>(tokenDataJson, JsonOptions);

                if (tokenData != null && !tokenData.Used && tokenData.ExpiresUtc > DateTime.UtcNow)
                {
                    activeTokensCount++;
                }
            }
            catch
            {
                // Skip invalid tokens during statistics gathering
            }
        }

        return new
        {
            passwordHistoryEntries = passwordHistoryCount,
            totalResetTokens = resetTokensCount,
            activeResetTokens = activeTokensCount,
            expiredTokens = resetTokensCount - activeTokensCount,
            timestamp = DateTime.UtcNow
        };
    }





    private class CleanupResults
    {
        public int TokensProcessed { get; set; }
        public int TokensRemoved { get; set; }
    }

    private class ExpirationNotificationResults
    {
        public int UsersNotified { get; set; }
        public int EmailsSent { get; set; }
    }
}
