# Test Run Report — 2025-08-15

## Summary
End-to-end validation of core user lifecycle in Power Pages + Azure Functions:
- Invitation → Registration → Sign-in → Forgot Password Reset
- Document records what has been verified and what remains to test.

## Environment
- Date/Time (local): 2025-08-15 18:30 ET
- Function App base URL: <fill>
- Function Key: <fill>
- Application (department): <fill>
- Test User Email: <fill>

## Completed Checks

- [x] Invite user via Power Pages/admin UI
  - Function: `InvitationFunction` → `InvitationService?operation=invite-user`
  - Result: Invitation email received
  - Correlation ID(s): <fill>

- [x] Registration with invitation code
  - Function: `RegistrationFunction` → `RegistrationService?operation=register`
  - Validation used: `InvitationTokenManager.ValidateInvitationByCode(...)` (or token+code if provided)
  - Result: User account created in Entra External ID
  - Correlation ID(s): <fill>

- [x] Sign in with the new account (Power Pages)
  - Auth handled by Entra External ID (native)
  - Result: Successful redirect to authenticated area
  - Evidence: Screenshot/URL/logs <attach>

- [x] Forgot Password: Initiate
  - Function: `PasswordFunction` → `PasswordService?operation=reset-initiate`
  - Result: Received reset email (token stored server-side, verification code in URL)
  - Correlation ID(s): <fill>

- [x] Forgot Password: Complete
  - Function: `PasswordFunction` → `PasswordService?operation=reset-complete`
  - Inputs: token + verificationCode + newPassword
  - Result: Password updated in Entra, token marked used, history updated, notification sent
  - Correlation ID(s): <fill>

## Findings from Latest Run (2025-08-15 18:39 ET)

- [x] Password Reuse Enforcement
  - Outcome: Reuse was correctly blocked by backend.
  - Issue: No error shown on the current page when blocked.
  - Notes: Response pattern is returned (`version: "1.0.0", action: "ShowBlockPage"`) but UI did not surface it.

- [x] Password Change Success
  - Outcome: Changed to a new password successfully and logged in with the new password.
  - Observation: After using a reset token, visiting the reset link still loads the web page.
  - Hypothesis: Frontend page allows rendering within token validity window; server marks token used but UI is not gating access on "used" status.

- [x] Invitation After Account Creation
  - Outcome: Attempting to use invitation after account creation fails as expected.
  - Status: Invitation is marked used/invalid properly.

- [x] Email Notifications
  - Outcome: All expected emails are being delivered (invitation, password changed, etc.).

## Action Items Based on Findings

- [frontend] Surface password reuse error on page
  - Handle 409/Conflict responses and messages from `PasswordFunction.HandlePasswordValidation()`
  - If response contains `action: "ShowBlockPage"`, display the `userMessage` prominently.

- [frontend + backend] Block reset page when token already used
  - On page load, call a lightweight validate endpoint (or current reset-verify path if available) to check token status.
  - If token is used/expired, redirect to an error or start-over page instead of rendering reset form.
  - Verify `_resetTokenManager.MarkTokenAsUsed(...)` is called and persisted; add logging to confirm state.

- [observability] Log correlation IDs in UI
  - Capture and surface `correlationId` in UI console (non-production) to speed up cross-service tracing.

- [optional] Add explicit “used” indicator in validate responses
  - Ensure validation responses include a clear `used: true/false` where appropriate to simplify UI gating.

## Changes Implemented (2025-08-15 19:03 ET)

- [x] Backend: Added `PasswordService?operation=validate-reset-token` in `PasswordFunction.cs`
  - Purpose: Lightweight validation to check token state before rendering reset form.
  - Expected response shape (example): `{ valid: true|false, used: true|false, expiresUtc, email }`.

- [x] Frontend: Updated `PowerPages Files/reset-password.js`
  - New behavior: On page load, calls `validate-reset-token` with the `token` from query string.
  - If `!valid` or `used`, disables form and shows an error/redirects to error page.
  - Prevents rendering of actionable reset UI for used/expired tokens.

## Next Checks (Planned)

- [x] Password reuse enforcement (history)
  - Result: Blocked as expected.
  - Follow-up: UI must show error message on block (see Action Items).

- [x] Password change success + subsequent reuse block
  - Result: New password works for login. Reuse of known password was blocked.
  - Note: Ensure UI displays error appropriately on block.

- [x] Reset token reuse prevention
  - Implemented: Pre-render validation via `validate-reset-token` and UI gating in `reset-password.js`.
  - Retest: Use a token that has already been used. Expect the page to show an error/redirect and the form to be disabled.

- [ ] Rate limiting behavior
  - Rapid calls to a single operation (e.g., `reset-initiate`)
  - Expect: `{ success:false, errorCode:"RateLimitExceeded", retryAfter: ... }`

- [ ] Invitation token diagnostic validation (optional)
  - `InvitationService?operation=validate-token` with a token
  - Expect: success with email/appId if valid; 401 otherwise

- [ ] Utility health
  - `UtilityService?operation=health` → status and blob storage health

- [ ] Utility stats and cleanup
  - `UtilityService?operation=stats` → counts for history and tokens
  - `UtilityService?operation=cleanup-tokens` → tokensProcessed/tokensRemoved

- [ ] Notification jobs (optional)
  - `UtilityService?operation=notify-expiring-passwords`
  - Confirm expiration/expired emails (requires data + env vars)

## Evidence Log (to fill during/after tests)

- Correlation IDs per call:
  - Invitation: <fill>
  - Registration: <fill>
  - Reset-initiate: <fill>
  - Reset-complete: <fill>
  - Others: <fill>

- Email confirmations:
  - Invitation email: received (Y/N)
  - Account created: received (Y/N)
  - Password changed: received (Y/N)
  - Expiration/Expired: received (Y/N)

- Screenshots/Artifacts:
  - Sign-in success: <attach>
  - Block page on reuse (if tested): <attach>

## Notes
- All responses include `correlationId` for tracing across services.
- Application scoping is enforced via Graph filters including `department eq '{applicationName}'` in password reset flows.
- Use this document as a running log for QA; duplicate and date-stamp for future runs.
